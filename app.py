from flask import Flask, render_template, request, jsonify

# 创建Flask应用实例
app = Flask(__name__)

# 配置
app.config['SECRET_KEY'] = 'your-secret-key-here'
app.config['DEBUG'] = True

# 主页路由
@app.route('/')
def index():
    return render_template('index.html')

# API示例路由
@app.route('/api/hello')
def api_hello():
    return jsonify({
        'message': 'Hello from Flask API!',
        'status': 'success'
    })

# 带参数的路由示例
@app.route('/user/<username>')
def user_profile(username):
    return render_template('user.html', username=username)

# POST请求示例
@app.route('/api/data', methods=['POST'])
def handle_data():
    data = request.get_json()
    return jsonify({
        'received': data,
        'message': 'Data received successfully'
    })

# 错误处理
@app.errorhandler(404)
def not_found(error):
    return render_template('404.html'), 404

@app.errorhandler(500)
def internal_error(error):
    return render_template('500.html'), 500

if __name__ == '__main__':
    app.run(debug=True, host='0.0.0.0', port=5000)
